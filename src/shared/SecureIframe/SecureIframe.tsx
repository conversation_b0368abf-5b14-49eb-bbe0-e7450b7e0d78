import React, {
  useState, forwardRef,
} from 'react';
import { Box } from '@mui/material';
import Loader from 'shared/Loader';

const DEFAULT_SANDBOX = 'allow-same-origin allow-scripts allow-forms allow-popups';

export interface SecureIframeProps extends React.IframeHTMLAttributes<HTMLIFrameElement> {
  src: string;
  title?: string;
  className?: string;
  style?: React.CSSProperties;
  showLoading?: boolean;
  onLoad?: () => void;
  sandbox?: string;
}

const SecureIframe = forwardRef<HTMLIFrameElement, SecureIframeProps>(({
  src,
  title,
  className = '',
  style = {},
  showLoading = true,
  onLoad,
  sandbox = DEFAULT_SANDBOX,
  ...rest
}, ref) => {
  const [status, setStatus] = useState<'loading' | 'loaded' | 'error'>('loading');

  const handleLoad = () => {
    setStatus('loaded');
    onLoad?.();
  };

  return (
    <Box
      className={className}
    >
      {status === 'loading' && showLoading && (
        <div data-testid="loader"><Loader /></div>
      )}

      <iframe
        ref={ref}
        src={src}
        title={title}
        style={{
          ...style,
        }}
        sandbox={sandbox}
        allowFullScreen
        onLoad={handleLoad}
        {...rest}
      />
    </Box>
  );
});

SecureIframe.displayName = 'SecureIframe';

export default SecureIframe;
