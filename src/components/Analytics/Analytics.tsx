import React, { FC, useCallback } from 'react';
import { useAppContext } from 'AppContextProvider';
import SecureIframe from '../../shared/SecureIframe/SecureIframe';
import './Analytics.scss';

const Analytics: FC = () => {
  const { appConfig } = useAppContext();
  const analyticsUrl = (appConfig?.analyticsUrl as string);

  const handleLoad = useCallback(() => {
    console.log('Analytics iframe loaded successfully');
  }, []);

  return (
    <div className="analyticsContainer" data-testid="analytics-container">
      <SecureIframe
        src={analyticsUrl}
        title="Superset Analytics Panel"
        className="analyticsIframe"
        onLoad={handleLoad}
        showLoading
        sandbox="allow-same-origin allow-scripts allow-forms allow-popups allow-top-navigation allow-downloads"
      />
    </div>
  );
};

export default Analytics;
